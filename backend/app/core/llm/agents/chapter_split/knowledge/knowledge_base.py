"""
章节分割知识库
负责管理章节分割规则的存储、查询、统计和优化
"""

import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func
from loguru import logger
from threading import Lock
from collections import defaultdict

from ....database.models.novel import ChapterRegexPattern


class ChapterKnowledgeBase:
    """
    章节分割知识库服务

    提供智能的规则管理、分页查询、统计更新等功能
    替换原有的RuleManager，提供更高级的业务逻辑
    """

    def __init__(self, db_session: Session):
        """
        初始化知识库

        Args:
            db_session: 数据库会话
        """
        self.db = db_session

        # 配置参数
        self.config = {
            'page_size': 100,           # 默认分页大小
            'max_test_rules': 500,      # 最大测试规则数
            'update_delay': 1.0,        # 更新延迟时间(秒)
            'sort_order': 'hit_count_desc',  # 排序规则
            'enable_cleanup': False,    # 是否启用规则清理
            'cleanup_days': 30,         # 清理天数阈值
        }

        # 延迟更新相关
        self._pending_updates = defaultdict(int)  # {pattern_id: hit_increment}
        self._last_update_time = time.time()
        self._update_lock = Lock()

        logger.info("章节分割知识库初始化完成")

    def set_config(self, key: str, value: Any) -> None:
        """
        设置配置参数

        Args:
            key: 配置键
            value: 配置值
        """
        if key in self.config:
            self.config[key] = value
            logger.debug(f"更新配置: {key} = {value}")
        else:
            logger.warning(f"未知配置项: {key}")

    def get_config(self, key: str) -> Any:
        """
        获取配置参数

        Args:
            key: 配置键

        Returns:
            配置值
        """
        return self.config.get(key)

    def initialize_default_patterns(self) -> None:
        """
        初始化默认规则

        将硬编码的默认规则写入数据库（如果不存在）
        """
        logger.info("开始初始化默认章节分割规则...")

        # 检查是否已有默认规则
        existing_count = self.db.query(ChapterRegexPattern).filter_by(source='init').count()
        if existing_count > 0:
            logger.info(f"已存在 {existing_count} 条默认规则，跳过初始化")
            return

        # 默认规则定义（从原有代码迁移）
        default_rules = [
            # intro类型
            ("intro", r"序章.*?[\n\r]"),
            ("intro", r"前言.*?[\n\r]"),
            ("intro", r"楔子.*?[\n\r]"),
            ("intro", r"引子.*?[\n\r]"),
            ("intro", r"引言.*?[\n\r]"),
            ("intro", r"序幕.*?[\n\r]"),
            ("intro", r"简介.*?[\n\r]"),
            ("intro", r"前序.*?[\n\r]"),

            # anywhere类型
            ("anywhere", r"尾声.*?[\n\r]"),
            ("anywhere", r"后记.*?[\n\r]"),
            ("anywhere", r"终章.*?[\n\r]"),
            ("anywhere", r"后序.*?[\n\r]"),

            # regular类型
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+章.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+节.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+卷.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+集.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+部.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+篇.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+回.*?[\n\r]"),
            ("regular", r"[Cc][Hh][Aa][Pp][Tt][Ee][Rr]\\s*[\\d]+.*?[\\n\\r]"),
            ("regular", r"第\\s*\\d+\\s*章.*?[\\n\\r]"),
            ("regular", r"\\d+[\\.、]\\s*[^\\n]+[\\n\\r]"),
            ("regular", r"正文卷\\s*第[零一二三四五六七八九十百千万亿\\d]+章.*?[\\n\\r]"),
        ]

        # 批量插入默认规则
        added_count = 0
        for pattern_type, pattern in default_rules:
            try:
                # 检查是否已存在相同规则
                existing = self.db.query(ChapterRegexPattern).filter_by(
                    pattern=pattern,
                    pattern_type=pattern_type
                ).first()

                if not existing:
                    rule = ChapterRegexPattern(
                        pattern=pattern,
                        pattern_type=pattern_type,
                        source='init',
                        is_active=True,
                        hit_count=0,
                        created_at=datetime.utcnow(),
                        last_hit_at=None
                    )
                    self.db.add(rule)
                    added_count += 1

            except Exception as e:
                logger.error(f"添加默认规则失败 {pattern}: {e}")

        # 提交事务
        try:
            self.db.commit()
            logger.success(f"成功初始化 {added_count} 条默认规则")
        except Exception as e:
            self.db.rollback()
            logger.error(f"提交默认规则失败: {e}")
            raise

    def get_patterns_page(self,
                         pattern_type: str,
                         page: int = 1,
                         page_size: Optional[int] = None) -> List[ChapterRegexPattern]:
        """
        分页获取指定类型的规则

        Args:
            pattern_type: 规则类型 (intro/regular/anywhere)
            page: 页码，从1开始
            page_size: 分页大小，None则使用配置默认值

        Returns:
            规则列表
        """
        if page_size is None:
            page_size = self.config['page_size']

        # 构建查询
        query = self.db.query(ChapterRegexPattern).filter(
            ChapterRegexPattern.pattern_type == pattern_type,
            ChapterRegexPattern.is_active == True
        )

        # 应用排序
        sort_order = self.config['sort_order']
        if sort_order == 'hit_count_desc':
            query = query.order_by(desc(ChapterRegexPattern.hit_count), asc(ChapterRegexPattern.created_at))
        elif sort_order == 'hit_count_asc':
            query = query.order_by(asc(ChapterRegexPattern.hit_count), asc(ChapterRegexPattern.created_at))
        elif sort_order == 'created_desc':
            query = query.order_by(desc(ChapterRegexPattern.created_at))
        elif sort_order == 'created_asc':
            query = query.order_by(asc(ChapterRegexPattern.created_at))
        else:
            # 默认排序
            query = query.order_by(desc(ChapterRegexPattern.hit_count), asc(ChapterRegexPattern.created_at))

        # 应用分页
        offset = (page - 1) * page_size
        patterns = query.offset(offset).limit(page_size).all()

        logger.debug(f"获取 {pattern_type} 规则第 {page} 页，共 {len(patterns)} 条")
        return patterns

    def get_total_count(self, pattern_type: str) -> int:
        """
        获取指定类型的规则总数

        Args:
            pattern_type: 规则类型

        Returns:
            规则总数
        """
        count = self.db.query(ChapterRegexPattern).filter(
            ChapterRegexPattern.pattern_type == pattern_type,
            ChapterRegexPattern.is_active == True
        ).count()

        return count

    def get_max_pages(self, pattern_type: str, page_size: Optional[int] = None) -> int:
        """
        获取指定类型的最大页数

        Args:
            pattern_type: 规则类型
            page_size: 分页大小

        Returns:
            最大页数
        """
        if page_size is None:
            page_size = self.config['page_size']

        total_count = self.get_total_count(pattern_type)
        max_pages = (total_count + page_size - 1) // page_size  # 向上取整

        return max_pages

    def update_hit_count(self, pattern_id: int) -> None:
        """
        更新规则命中次数（延迟更新）

        Args:
            pattern_id: 规则ID
        """
        with self._update_lock:
            self._pending_updates[pattern_id] += 1

            # 检查是否需要刷新更新
            current_time = time.time()
            if current_time - self._last_update_time >= self.config['update_delay']:
                self._flush_updates()
                self._last_update_time = current_time

    def _flush_updates(self) -> None:
        """
        刷新所有待更新的统计数据
        """
        if not self._pending_updates:
            return

        try:
            # 批量更新数据库
            for pattern_id, increment in self._pending_updates.items():
                pattern = self.db.query(ChapterRegexPattern).filter_by(id=pattern_id).first()
                if pattern:
                    pattern.hit_count = (pattern.hit_count or 0) + increment
                    pattern.last_hit_at = datetime.utcnow()

            self.db.commit()

            # 清空待更新列表
            update_count = len(self._pending_updates)
            self._pending_updates.clear()

            logger.debug(f"批量更新了 {update_count} 条规则的命中统计")

        except Exception as e:
            self.db.rollback()
            logger.error(f"批量更新规则统计失败: {e}")

    def flush_updates(self) -> None:
        """
        强制刷新所有待更新的统计数据（公开方法）
        """
        with self._update_lock:
            self._flush_updates()

    def add_pattern(self,
                   pattern: str,
                   pattern_type: str,
                   source: str = 'llm',
                   initial_hit_count: int = 0) -> ChapterRegexPattern:
        """
        添加新规则（带去重检查）

        Args:
            pattern: 正则表达式
            pattern_type: 规则类型
            source: 规则来源
            initial_hit_count: 初始命中次数

        Returns:
            规则对象（新增或已存在）
        """
        try:
            # 检查是否已存在相同规则
            existing = self.db.query(ChapterRegexPattern).filter_by(
                pattern=pattern,
                pattern_type=pattern_type
            ).first()

            if existing:
                if not existing.is_active:
                    # 重新激活已存在的规则
                    existing.is_active = True
                    self.db.commit()
                    logger.info(f"重新激活已存在规则: {pattern}")
                else:
                    logger.debug(f"规则已存在，跳过添加: {pattern}")
                return existing

            # 创建新规则
            new_rule = ChapterRegexPattern(
                pattern=pattern,
                pattern_type=pattern_type,
                source=source,
                is_active=True,
                hit_count=initial_hit_count,
                created_at=datetime.utcnow(),
                last_hit_at=None
            )

            self.db.add(new_rule)
            self.db.commit()

            logger.info(f"添加新规则: {pattern_type} - {pattern}")
            return new_rule

        except Exception as e:
            self.db.rollback()
            logger.error(f"添加规则失败 {pattern}: {e}")
            raise

    def get_pattern_by_id(self, pattern_id: int) -> Optional[ChapterRegexPattern]:
        """
        根据ID获取规则

        Args:
            pattern_id: 规则ID

        Returns:
            规则对象或None
        """
        return self.db.query(ChapterRegexPattern).filter_by(id=pattern_id).first()

    def disable_pattern(self, pattern_id: int) -> bool:
        """
        禁用规则

        Args:
            pattern_id: 规则ID

        Returns:
            是否成功
        """
        try:
            pattern = self.get_pattern_by_id(pattern_id)
            if pattern and pattern.is_active:
                pattern.is_active = False
                self.db.commit()
                logger.info(f"禁用规则: {pattern.pattern}")
                return True
            return False
        except Exception as e:
            self.db.rollback()
            logger.error(f"禁用规则失败: {e}")
            return False

    def enable_pattern(self, pattern_id: int) -> bool:
        """
        启用规则

        Args:
            pattern_id: 规则ID

        Returns:
            是否成功
        """
        try:
            pattern = self.get_pattern_by_id(pattern_id)
            if pattern and not pattern.is_active:
                pattern.is_active = True
                self.db.commit()
                logger.info(f"启用规则: {pattern.pattern}")
                return True
            return False
        except Exception as e:
            self.db.rollback()
            logger.error(f"启用规则失败: {e}")
            return False

    def get_all_pattern_types(self) -> List[str]:
        """
        获取所有可用的规则类型

        Returns:
            规则类型列表
        """
        return ['intro', 'regular', 'anywhere']

    def get_patterns_iterator(self,
                            pattern_types: Optional[List[str]] = None,
                            max_rules: Optional[int] = None) -> Tuple[List[ChapterRegexPattern], bool]:
        """
        获取规则迭代器，支持按类型分页遍历所有规则

        Args:
            pattern_types: 要查询的规则类型列表，None表示所有类型
            max_rules: 最大规则数量限制

        Returns:
            (规则列表, 是否还有更多规则)
        """
        if pattern_types is None:
            pattern_types = self.get_all_pattern_types()

        if max_rules is None:
            max_rules = self.config['max_test_rules']

        all_patterns = []
        total_tested = 0

        for pattern_type in pattern_types:
            page = 1
            page_size = self.config['page_size']

            while total_tested < max_rules:
                patterns = self.get_patterns_page(pattern_type, page, page_size)

                if not patterns:
                    # 当前类型没有更多规则
                    break

                # 添加规则，但不超过限制
                remaining_quota = max_rules - total_tested
                patterns_to_add = patterns[:remaining_quota]
                all_patterns.extend(patterns_to_add)
                total_tested += len(patterns_to_add)

                if len(patterns_to_add) < len(patterns):
                    # 达到限制，提前结束
                    return all_patterns, True

                if len(patterns) < page_size:
                    # 当前类型的规则已全部获取
                    break

                page += 1

        # 检查是否还有更多规则
        has_more = total_tested >= max_rules
        for pattern_type in pattern_types:
            if not has_more:
                total_count = self.get_total_count(pattern_type)
                tested_count = sum(1 for p in all_patterns if p.pattern_type == pattern_type)
                if tested_count < total_count:
                    has_more = True
                    break

        return all_patterns, has_more

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息

        Returns:
            统计信息字典
        """
        stats = {}

        for pattern_type in self.get_all_pattern_types():
            total_count = self.get_total_count(pattern_type)

            # 获取该类型的命中统计
            patterns = self.db.query(ChapterRegexPattern).filter(
                ChapterRegexPattern.pattern_type == pattern_type,
                ChapterRegexPattern.is_active == True
            ).all()

            total_hits = sum(p.hit_count or 0 for p in patterns)
            avg_hits = total_hits / total_count if total_count > 0 else 0

            # 获取最近使用的规则数量
            recent_used = sum(1 for p in patterns if p.last_hit_at is not None)

            stats[pattern_type] = {
                'total_count': total_count,
                'total_hits': total_hits,
                'avg_hits': round(avg_hits, 2),
                'recent_used': recent_used,
                'usage_rate': round(recent_used / total_count * 100, 1) if total_count > 0 else 0
            }

        # 整体统计
        stats['overall'] = {
            'total_patterns': sum(stats[pt]['total_count'] for pt in self.get_all_pattern_types()),
            'total_hits': sum(stats[pt]['total_hits'] for pt in self.get_all_pattern_types()),
            'pending_updates': len(self._pending_updates),
            'config': self.config.copy()
        }

        return stats

    def cleanup_unused_patterns(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        清理未使用的规则

        Args:
            dry_run: 是否只是预览，不实际删除

        Returns:
            清理结果
        """
        if not self.config['enable_cleanup']:
            return {'error': '规则清理功能未启用'}

        cleanup_threshold = datetime.utcnow() - timedelta(days=self.config['cleanup_days'])

        # 查找需要清理的规则
        candidates = self.db.query(ChapterRegexPattern).filter(
            ChapterRegexPattern.is_active == True,
            ChapterRegexPattern.hit_count == 0,
            ChapterRegexPattern.created_at < cleanup_threshold,
            ChapterRegexPattern.source != 'init'  # 不清理默认规则
        ).all()

        result = {
            'candidates_count': len(candidates),
            'candidates': [
                {
                    'id': p.id,
                    'pattern': p.pattern,
                    'pattern_type': p.pattern_type,
                    'source': p.source,
                    'created_at': p.created_at.isoformat() if p.created_at else None
                }
                for p in candidates
            ],
            'dry_run': dry_run
        }

        if not dry_run and candidates:
            try:
                # 禁用这些规则而不是删除
                for pattern in candidates:
                    pattern.is_active = False

                self.db.commit()
                result['cleaned_count'] = len(candidates)
                logger.info(f"清理了 {len(candidates)} 条未使用的规则")

            except Exception as e:
                self.db.rollback()
                result['error'] = str(e)
                logger.error(f"清理规则失败: {e}")

        return result

    def __del__(self):
        """
        析构函数，确保在对象销毁时刷新所有待更新的数据
        """
        try:
            if hasattr(self, '_pending_updates') and self._pending_updates:
                self.flush_updates()
        except Exception as e:
            logger.error(f"析构时刷新更新失败: {e}")

    def get_pending_updates_count(self) -> int:
        """
        获取待更新的规则数量

        Returns:
            待更新数量
        """
        return len(self._pending_updates)

    def force_update_pattern_stats(self, pattern_id: int, hit_increment: int = 1) -> bool:
        """
        强制立即更新规则统计（不使用延迟机制）

        Args:
            pattern_id: 规则ID
            hit_increment: 命中次数增量

        Returns:
            是否成功
        """
        try:
            pattern = self.get_pattern_by_id(pattern_id)
            if pattern:
                pattern.hit_count = (pattern.hit_count or 0) + hit_increment
                pattern.last_hit_at = datetime.utcnow()
                self.db.commit()
                logger.debug(f"立即更新规则 {pattern_id} 统计: +{hit_increment}")
                return True
            return False
        except Exception as e:
            self.db.rollback()
            logger.error(f"立即更新规则统计失败: {e}")
            return False

    def batch_add_patterns(self, patterns_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量添加规则

        Args:
            patterns_data: 规则数据列表，每个元素包含pattern, pattern_type, source等字段

        Returns:
            添加结果
        """
        result = {
            'total': len(patterns_data),
            'added': 0,
            'skipped': 0,
            'errors': []
        }

        try:
            for data in patterns_data:
                try:
                    pattern = data.get('pattern')
                    pattern_type = data.get('pattern_type')
                    source = data.get('source', 'batch')
                    initial_hit_count = data.get('initial_hit_count', 0)

                    if not pattern or not pattern_type:
                        result['errors'].append(f"缺少必要字段: {data}")
                        continue

                    # 检查是否已存在
                    existing = self.db.query(ChapterRegexPattern).filter_by(
                        pattern=pattern,
                        pattern_type=pattern_type
                    ).first()

                    if existing:
                        result['skipped'] += 1
                        continue

                    # 创建新规则
                    new_rule = ChapterRegexPattern(
                        pattern=pattern,
                        pattern_type=pattern_type,
                        source=source,
                        is_active=True,
                        hit_count=initial_hit_count,
                        created_at=datetime.utcnow(),
                        last_hit_at=None
                    )

                    self.db.add(new_rule)
                    result['added'] += 1

                except Exception as e:
                    result['errors'].append(f"处理规则失败 {data}: {e}")

            # 批量提交
            self.db.commit()
            logger.info(f"批量添加规则完成: 总数={result['total']}, 新增={result['added']}, 跳过={result['skipped']}")

        except Exception as e:
            self.db.rollback()
            result['errors'].append(f"批量提交失败: {e}")
            logger.error(f"批量添加规则失败: {e}")

        return result

    def reset_pattern_stats(self, pattern_type: Optional[str] = None) -> Dict[str, Any]:
        """
        重置规则统计信息

        Args:
            pattern_type: 规则类型，None表示重置所有类型

        Returns:
            重置结果
        """
        try:
            query = self.db.query(ChapterRegexPattern)
            if pattern_type:
                query = query.filter(ChapterRegexPattern.pattern_type == pattern_type)

            patterns = query.all()
            reset_count = 0

            for pattern in patterns:
                pattern.hit_count = 0
                pattern.last_hit_at = None
                reset_count += 1

            self.db.commit()

            # 清空待更新列表
            with self._update_lock:
                if pattern_type:
                    # 只清空指定类型的待更新项
                    to_remove = []
                    for pid in self._pending_updates:
                        p = self.get_pattern_by_id(pid)
                        if p and p.pattern_type == pattern_type:
                            to_remove.append(pid)
                    for pid in to_remove:
                        del self._pending_updates[pid]
                else:
                    # 清空所有待更新项
                    self._pending_updates.clear()

            result = {
                'reset_count': reset_count,
                'pattern_type': pattern_type or 'all'
            }

            logger.info(f"重置了 {reset_count} 条规则的统计信息")
            return result

        except Exception as e:
            self.db.rollback()
            logger.error(f"重置规则统计失败: {e}")
            return {'error': str(e)}