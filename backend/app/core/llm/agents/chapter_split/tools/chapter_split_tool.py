"""
章节分割工具
负责具体的章节分割逻辑和验证
"""

from typing import List, Dict, Any, Optional
from loguru import logger

try:
    from ....base import BaseTool
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BaseTool

from .regex_tool import RegexTool


class ChapterSplitTool(BaseTool):
    """
    章节分割工具

    负责执行具体的章节分割逻辑
    包括模式匹配、内容提取、结果验证等
    """

    def __init__(self):
        super().__init__(
            name="chapter_split_tool",
            description="章节分割核心工具，负责具体的分割逻辑"
        )

        # 初始化依赖的工具
        self.regex_tool = RegexTool()

        # 分割配置
        self.config = {
            'min_chapter_length': 100,      # 最小章节长度
            'max_chapter_length': 100000,   # 最大章节长度
            'min_chapters': 2,              # 最少章节数
            'default_chapter_length': 3000, # 默认章节长度（用于长度分割）
        }

    def execute(self, **kwargs) -> Any:
        """
        执行章节分割操作

        支持的操作：
        - split_by_patterns: 使用模式分割
        - split_by_length: 按长度分割
        - validate_chapters: 验证章节
        - merge_short_chapters: 合并短章节
        """
        operation = kwargs.get("operation")

        if operation == "split_by_patterns":
            return self._split_by_patterns(**kwargs)
        elif operation == "split_by_length":
            return self._split_by_length(**kwargs)
        elif operation == "validate_chapters":
            return self._validate_chapters(**kwargs)
        elif operation == "merge_short_chapters":
            return self._merge_short_chapters(**kwargs)
        else:
            raise ValueError(f"不支持的操作: {operation}")

    def _split_by_patterns(self, content: str, patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        使用模式分割章节

        Args:
            content: 文本内容
            patterns: 模式列表

        Returns:
            分割结果
        """
        # 批量测试模式
        match_results = self.regex_tool.batch_match_patterns(content, patterns)

        # 找到第一个成功的模式
        for result in match_results:
            if result.get('success', False):
                # 使用该模式进行分割
                boundaries = self.regex_tool.find_chapter_boundaries(
                    content, result['pattern']
                )

                if boundaries:
                    chapters = self.regex_tool.extract_chapter_content(content, boundaries)

                    # 验证分割结果
                    validation = self._validate_chapters(chapters=chapters)

                    return {
                        'success': True,
                        'chapters': chapters,
                        'pattern_used': result['pattern'],
                        'pattern_id': result['pattern_id'],
                        'validation': validation,
                        'chapter_count': len(chapters)
                    }

        # 没有找到有效模式
        return {
            'success': False,
            'error': '没有找到有效的分割模式',
            'tested_patterns': len(patterns),
            'match_results': match_results
        }

    def _split_by_length(self, content: str, chapter_length: Optional[int] = None) -> Dict[str, Any]:
        """
        按长度分割章节

        Args:
            content: 文本内容
            chapter_length: 章节长度，None使用默认值

        Returns:
            分割结果
        """
        if chapter_length is None:
            chapter_length = self.config['default_chapter_length']

        chapters = []
        total_length = len(content)
        current_pos = 0
        chapter_index = 1

        while current_pos < total_length:
            # 计算章节结束位置
            end_pos = min(current_pos + chapter_length, total_length)

            # 如果不是最后一章，尝试在句号或段落处断开
            if end_pos < total_length:
                # 向后查找合适的断点
                search_range = min(200, total_length - end_pos)
                for i in range(search_range):
                    char = content[end_pos + i]
                    if char in ['。', '！', '？', '\n\n']:
                        end_pos = end_pos + i + 1
                        break

            # 提取章节内容
            chapter_content = content[current_pos:end_pos].strip()

            if chapter_content:
                chapter = {
                    'chapter_index': chapter_index,
                    'title': f"第{chapter_index}章",
                    'content': chapter_content,
                    'word_count': len(chapter_content),
                    'start_pos': current_pos,
                    'end_pos': end_pos
                }
                chapters.append(chapter)
                chapter_index += 1

            current_pos = end_pos

        # 验证分割结果
        validation = self._validate_chapters(chapters=chapters)

        return {
            'success': True,
            'chapters': chapters,
            'method': 'length_based',
            'chapter_length': chapter_length,
            'validation': validation,
            'chapter_count': len(chapters)
        }

    def _validate_chapters(self, chapters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证章节分割结果

        Args:
            chapters: 章节列表

        Returns:
            验证结果
        """
        validation = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'stats': {}
        }

        if not chapters:
            validation['valid'] = False
            validation['errors'].append('没有找到任何章节')
            return validation

        # 检查章节数量
        chapter_count = len(chapters)
        if chapter_count < self.config['min_chapters']:
            validation['warnings'].append(f'章节数量过少: {chapter_count}')

        # 检查章节长度
        short_chapters = []
        long_chapters = []
        total_words = 0

        for chapter in chapters:
            word_count = chapter.get('word_count', 0)
            total_words += word_count

            if word_count < self.config['min_chapter_length']:
                short_chapters.append(chapter['chapter_index'])
            elif word_count > self.config['max_chapter_length']:
                long_chapters.append(chapter['chapter_index'])

        if short_chapters:
            validation['warnings'].append(f'章节过短: {short_chapters}')

        if long_chapters:
            validation['warnings'].append(f'章节过长: {long_chapters}')

        # 计算统计信息
        word_counts = [ch.get('word_count', 0) for ch in chapters]
        validation['stats'] = {
            'chapter_count': chapter_count,
            'total_words': total_words,
            'avg_words_per_chapter': total_words // chapter_count if chapter_count > 0 else 0,
            'min_words': min(word_counts) if word_counts else 0,
            'max_words': max(word_counts) if word_counts else 0,
            'short_chapters_count': len(short_chapters),
            'long_chapters_count': len(long_chapters)
        }

        return validation

    def _merge_short_chapters(self, chapters: List[Dict[str, Any]], min_length: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        合并过短的章节

        Args:
            chapters: 原始章节列表
            min_length: 最小章节长度

        Returns:
            合并后的章节列表
        """
        if min_length is None:
            min_length = self.config['min_chapter_length']

        if not chapters:
            return chapters

        merged_chapters = []
        current_chapter = None

        for chapter in chapters:
            word_count = chapter.get('word_count', 0)

            if current_chapter is None:
                current_chapter = chapter.copy()
            elif word_count < min_length or current_chapter.get('word_count', 0) < min_length:
                # 合并到当前章节
                current_chapter['content'] += '\n\n' + chapter['content']
                current_chapter['word_count'] = len(current_chapter['content'])
                current_chapter['end_pos'] = chapter['end_pos']

                # 更新标题
                if '合并' not in current_chapter['title']:
                    current_chapter['title'] += f' (合并至第{chapter["chapter_index"]}章)'
            else:
                # 当前章节足够长，保存并开始新章节
                merged_chapters.append(current_chapter)
                current_chapter = chapter.copy()

        # 添加最后一个章节
        if current_chapter:
            merged_chapters.append(current_chapter)

        # 重新编号
        for i, chapter in enumerate(merged_chapters):
            chapter['chapter_index'] = i + 1

        return merged_chapters

    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        operation = kwargs.get("operation")
        if not operation:
            return False

        if operation == "split_by_patterns":
            return "content" in kwargs and "patterns" in kwargs
        elif operation == "split_by_length":
            return "content" in kwargs
        elif operation == "validate_chapters":
            return "chapters" in kwargs
        elif operation == "merge_short_chapters":
            return "chapters" in kwargs

        return False