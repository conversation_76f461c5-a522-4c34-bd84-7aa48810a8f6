"""
正则表达式工具
专门用于章节分割的正则表达式处理
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

try:
    from ....base import BaseTool
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BaseTool


class RegexTool(BaseTool):
    """
    正则表达式工具

    提供章节分割相关的正则表达式功能
    专注于模式匹配和文本处理
    """

    def __init__(self):
        super().__init__(
            name="regex_tool",
            description="正则表达式处理工具，用于章节分割"
        )

    def execute(self, **kwargs) -> Any:
        """
        执行正则表达式操作

        支持的操作：
        - match: 匹配文本
        - find_all: 查找所有匹配
        - split: 分割文本
        - validate: 验证正则表达式
        - batch_match: 批量匹配模式
        - find_boundaries: 查找章节边界
        """
        operation = kwargs.get("operation")

        if operation == "match":
            return self._match_text(**kwargs)
        elif operation == "find_all":
            return self._find_all_matches(**kwargs)
        elif operation == "split":
            return self._split_text(**kwargs)
        elif operation == "validate":
            return self._validate_regex(**kwargs)
        elif operation == "batch_match":
            return self.batch_match_patterns(**kwargs)
        elif operation == "find_boundaries":
            return self.find_chapter_boundaries(**kwargs)
        else:
            raise ValueError(f"不支持的操作: {operation}")

    def _match_text(self, pattern: str, text: str, flags: int = 0) -> Optional[Dict[str, Any]]:
        """匹配文本"""
        try:
            match = re.match(pattern, text, flags)
            if match:
                return {
                    "matched": True,
                    "groups": match.groups(),
                    "groupdict": match.groupdict(),
                    "span": match.span(),
                    "match_text": match.group(0)
                }
            return {"matched": False}
        except Exception as e:
            logger.error(f"正则匹配失败: {e}")
            return {"matched": False, "error": str(e)}

    def _find_all_matches(self, pattern: str, text: str, flags: int = 0) -> List[Dict[str, Any]]:
        """查找所有匹配"""
        try:
            matches = []
            for match in re.finditer(pattern, text, flags):
                matches.append({
                    "groups": match.groups(),
                    "groupdict": match.groupdict(),
                    "span": match.span(),
                    "match_text": match.group(0)
                })
            return matches
        except Exception as e:
            logger.error(f"正则查找失败: {e}")
            return []

    def _split_text(self, pattern: str, text: str, flags: int = 0) -> List[str]:
        """分割文本"""
        try:
            return re.split(pattern, text, flags=flags)
        except Exception as e:
            logger.error(f"正则分割失败: {e}")
            return [text]

    def _validate_regex(self, pattern: str) -> Dict[str, Any]:
        """验证正则表达式"""
        try:
            re.compile(pattern)
            return {"valid": True}
        except re.error as e:
            return {"valid": False, "error": str(e)}

    def validate_params(self, **kwargs) -> bool:
        """验证参数"""
        operation = kwargs.get("operation")
        if not operation:
            return False

        if operation in ["match", "find_all", "split"]:
            return "pattern" in kwargs and "text" in kwargs
        elif operation == "validate":
            return "pattern" in kwargs
        elif operation == "batch_match":
            return "content" in kwargs and "patterns" in kwargs
        elif operation == "find_boundaries":
            return "content" in kwargs and "pattern" in kwargs

        return False

    def batch_match_patterns(self, content: str, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量测试多个正则模式

        Args:
            content: 要匹配的文本内容
            patterns: 模式列表，每个包含pattern和id字段

        Returns:
            匹配结果列表
        """
        results = []

        for pattern_obj in patterns:
            pattern = pattern_obj.get('pattern', '')
            pattern_id = pattern_obj.get('id')

            try:
                matches = self._find_all_matches(pattern, content)

                result = {
                    'pattern_id': pattern_id,
                    'pattern': pattern,
                    'success': len(matches) > 1,  # 至少需要2个匹配才算成功
                    'match_count': len(matches),
                    'matches': matches
                }

                results.append(result)

                # 如果找到有效匹配，记录日志
                if result['success']:
                    logger.debug(f"找到有效模式: {pattern}, 匹配数: {len(matches)}")

            except re.error as e:
                logger.warning(f"正则表达式错误: {pattern} - {e}")
                results.append({
                    'pattern_id': pattern_id,
                    'pattern': pattern,
                    'success': False,
                    'error': str(e)
                })
            except Exception as e:
                logger.error(f"模式匹配失败: {pattern} - {e}")
                results.append({
                    'pattern_id': pattern_id,
                    'pattern': pattern,
                    'success': False,
                    'error': str(e)
                })

        return results

    def find_chapter_boundaries(self, content: str, pattern: str) -> List[Dict[str, Any]]:
        """
        查找章节边界

        Args:
            content: 文本内容
            pattern: 正则模式

        Returns:
            章节边界列表
        """
        try:
            matches = self._find_all_matches(pattern, content)
            boundaries = []

            for i, match in enumerate(matches):
                boundary = {
                    'index': i,
                    'title': match['match_text'].strip(),
                    'start_pos': match['span'][0],
                    'title_end_pos': match['span'][1],
                    'content_start_pos': match['span'][1]
                }

                # 计算章节内容结束位置
                if i + 1 < len(matches):
                    boundary['content_end_pos'] = matches[i + 1]['span'][0]
                else:
                    boundary['content_end_pos'] = len(content)

                boundaries.append(boundary)

            return boundaries

        except Exception as e:
            logger.error(f"查找章节边界失败: {e}")
            return []

    def extract_chapter_content(self, content: str, boundaries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据边界提取章节内容

        Args:
            content: 原始文本
            boundaries: 章节边界列表

        Returns:
            章节内容列表
        """
        chapters = []

        for boundary in boundaries:
            try:
                chapter_content = content[
                    boundary['content_start_pos']:boundary['content_end_pos']
                ].strip()

                # 跳过空章节
                if not chapter_content:
                    continue

                chapter = {
                    'chapter_index': boundary['index'] + 1,
                    'title': boundary['title'],
                    'content': chapter_content,
                    'word_count': len(chapter_content),
                    'start_pos': boundary['start_pos'],
                    'end_pos': boundary['content_end_pos']
                }

                chapters.append(chapter)

            except Exception as e:
                logger.error(f"提取章节内容失败: {e}")
                continue

        return chapters