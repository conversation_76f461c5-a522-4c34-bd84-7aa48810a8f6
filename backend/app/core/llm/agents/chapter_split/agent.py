"""
章节分割Agent - 垂直架构版本
基于新架构的专业章节分割智能体
"""

import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
from loguru import logger

from ...base import BaseAgent, AgentConfig
from ...agent_factory import register_agent
from .knowledge.knowledge_base import ChapterKnowledgeBase
from .memory.chapter_memory import ChapterMemory
from .tools.chapter_split_tool import ChapterSplitTool
from .tools.regex_tool import RegexTool

# 使用try-except处理导入，支持动态导入



@register_agent("chapter_split", {
    "description": "专业的章节分割智能体",
    "capabilities": ["智能章节分析", "正则表达式生成", "规则学习优化"],
    "version": "2.0.0"
})
class ChapterSplitAgent(BaseAgent):
    """
    章节分割专业Agent - 垂直架构版本
    
    专门负责小说章节分割任务，集成了：
    - 智能正则表达式生成
    - 章节结构分析  
    - 规则学习和优化
    """
    
    def __init__(self, config: AgentConfig, **kwargs):
        """
        初始化章节分割Agent

        Args:
            config: Agent配置
            **kwargs: 额外参数
        """
        super().__init__(config, **kwargs)

        # 获取数据库会话
        self.db_session = kwargs.get('db')
        if not self.db_session:
            raise ValueError("ChapterSplitAgent需要数据库会话参数")

        # 专业配置
        self.fuzzy_detection_threshold = 30000  # 3万字未找到章节则触发分析
        self.max_analysis_attempts = 3
        self.snippet_size = 3000

        # 初始化专用组件
        self._initialize_specialized_components()

        # 加载知识库
        self._load_knowledge_base()

        logger.info(f"章节分割Agent初始化完成 (垂直架构版本)")
    
    def _initialize_specialized_components(self):
        """初始化专用组件"""
        # 1. 初始化内存
        self.memory = ChapterMemory()

        # 2. 初始化工具
        self.chapter_split_tool = ChapterSplitTool()
        self.regex_tool = RegexTool()

        # 3. 专用提示模板 - 后续可以添加
        self.analysis_prompt = None
        self.evaluation_prompt = None

        logger.info("章节分割专用组件初始化完成")

    def _load_knowledge_base(self):
        """加载知识库"""
        # 初始化知识库
        self.knowledge_base = ChapterKnowledgeBase(self.db_session)

        # 初始化默认规则
        self.knowledge_base.initialize_default_patterns()

        # 配置知识库参数
        self.knowledge_base.set_config('page_size', 100)
        self.knowledge_base.set_config('max_test_rules', 500)
        self.knowledge_base.set_config('update_delay', 1.0)

        logger.info("章节分割知识库加载完成")
        
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理章节分割请求
        
        Args:
            input_data: 输入数据，包含：
                - content: 小说内容
                - novel_id: 小说ID (可选)
                - force_analysis: 是否强制分析 (可选)
                - method: 分割方法 (可选)
                
        Returns:
            处理结果，包含：
                - chapters: 章节列表
                - quality_score: 质量评分
                - method_used: 使用的方法
                - patterns_found: 发现的模式
        """
        return {"error": "章节分割Agent暂不支持独立运行"}    
      
    
    def smart_chapter_split(self, content: str, force_analysis: bool = False) -> List[Dict[str, Any]]:
        """
        智能章节分割 - 作为四个模块的调度中心

        Args:
            content: 小说内容
            force_analysis: 是否强制进行Agent分析

        Returns:
            章节列表
        """
        logger.info("开始智能章节分割")

        # 设置任务上下文到内存
        context = {
            'content_length': len(content),
            'force_analysis': force_analysis,
            'start_time': datetime.utcnow().isoformat(),
            'task_id': f"split_{hash(content[:1000]) % 10000}"
        }
        self.memory.set_context(context)

        try:
            # 1. 从知识库加载规则到内存
            if not force_analysis:
                active_patterns = self._load_patterns_from_knowledge_base()
                self.memory.store('active_patterns', active_patterns)

                # 2. 使用工具进行分割
                split_result = self.chapter_split_tool.execute(
                    operation="split_by_patterns",
                    content=content,
                    patterns=active_patterns
                )

                if split_result.get('success', False):
                    chapters = split_result['chapters']

                    # 更新知识库统计
                    self._update_pattern_stats(split_result)

                    # 缓存结果到内存
                    self.memory.cache_split_result(split_result)

                    logger.info(f"使用现有模式成功分割，章节数: {len(chapters)}")
                    return chapters

            # 3. 现有模式失败，使用LLM分析
            logger.info("现有模式无效，调用LLM分析...")
            new_patterns = self._llm_analyze_content(content)

            if new_patterns:
                # 将新模式存储到内存
                for pattern in new_patterns:
                    self.memory.add_new_pattern(pattern)

                # 添加到知识库
                self._add_new_patterns_to_knowledge_base(new_patterns)

                # 重新尝试分割
                split_result = self.chapter_split_tool.execute(
                    operation="split_by_patterns",
                    content=content,
                    patterns=new_patterns
                )

                if split_result.get('success', False):
                    chapters = split_result['chapters']
                    self.memory.cache_split_result(split_result)
                    logger.info(f"使用新模式成功分割，章节数: {len(chapters)}")
                    return chapters

            # 4. 降级方案：按长度分割
            logger.warning("所有模式都无效，使用长度分割")
            split_result = self.chapter_split_tool.execute(
                operation="split_by_length",
                content=content
            )

            chapters = split_result.get('chapters', [])
            self.memory.cache_split_result(split_result)

            return chapters

        except Exception as e:
            logger.error(f"智能分割失败: {e}")
            # 最后的降级方案
            return self._emergency_split(content)

        finally:
            # 更新性能统计到内存
            end_time = datetime.utcnow().isoformat()
            self.memory.update_performance_stats({
                'last_split_time': end_time,
                'content_length': len(content)
            })
    
    def _load_patterns_from_knowledge_base(self) -> List[Dict[str, Any]]:
        """从知识库加载活跃规则"""
        try:
            all_patterns = []

            # 按类型获取规则
            for pattern_type in ['intro', 'regular', 'anywhere']:
                patterns = self.knowledge_base.get_patterns_page(pattern_type, 1, 50)  # 每种类型取前50个

                # 转换为工具需要的格式
                for pattern_obj in patterns:
                    all_patterns.append({
                        'id': pattern_obj.id,
                        'pattern': pattern_obj.pattern,
                        'pattern_type': pattern_obj.pattern_type,
                        'hit_count': pattern_obj.hit_count or 0
                    })

            logger.debug(f"从知识库加载了 {len(all_patterns)} 个规则")
            return all_patterns

        except Exception as e:
            logger.error(f"加载规则失败: {e}")
            return []

    def _update_pattern_stats(self, split_result: Dict[str, Any]) -> None:
        """更新模式统计"""
        try:
            pattern_id = split_result.get('pattern_id')
            if pattern_id:
                self.knowledge_base.update_hit_count(pattern_id)
                logger.debug(f"更新规则统计: {pattern_id}")
        except Exception as e:
            logger.error(f"更新统计失败: {e}")

    def _llm_analyze_content(self, content: str) -> List[Dict[str, Any]]:
        """LLM分析内容，发现新规则"""
        # TODO: 实现LLM分析逻辑
        # 这里暂时返回空，后续可以集成LLM调用
        logger.warning("LLM分析功能暂未实现")
        return []

    def _add_new_patterns_to_knowledge_base(self, patterns: List[Dict[str, Any]]) -> None:
        """将新模式添加到知识库"""
        try:
            added_count = 0
            for pattern_data in patterns:
                pattern = pattern_data.get('pattern')
                pattern_type = pattern_data.get('pattern_type', 'regular')

                if pattern:
                    self.knowledge_base.add_pattern(
                        pattern=pattern,
                        pattern_type=pattern_type,
                        source='llm',
                        initial_hit_count=1
                    )
                    added_count += 1

            if added_count > 0:
                logger.info(f"添加了 {added_count} 个新模式到知识库")

        except Exception as e:
            logger.error(f"添加新模式失败: {e}")

    def _emergency_split(self, content: str) -> List[Dict[str, Any]]:
        """紧急分割方案"""
        try:
            # 使用工具的长度分割
            result = self.chapter_split_tool.execute(
                operation="split_by_length",
                content=content,
                chapter_length=2000  # 较短的章节长度
            )

            return result.get('chapters', [])

        except Exception as e:
            logger.error(f"紧急分割失败: {e}")
            # 最基础的分割
            return [{
                'chapter_index': 1,
                'title': '全文',
                'content': content,
                'word_count': len(content),
                'start_pos': 0,
                'end_pos': len(content)
            }]
    
    def _create_chapters_from_regex_matches(self, content: str, matches: List, pattern_obj) -> List[Dict[str, Any]]:
        """根据正则匹配结果创建章节"""
        chapters = []

        for i, match in enumerate(matches):
            # 获取章节标题
            title = match.group().strip()

            # 计算章节内容范围
            start_pos = match.end()  # 标题后开始
            if i + 1 < len(matches):
                end_pos = matches[i + 1].start()  # 下一个标题前结束
            else:
                end_pos = len(content)  # 最后一章到文末

            # 提取章节内容
            chapter_content = content[start_pos:end_pos].strip()

            # 跳过空章节
            if not chapter_content:
                continue

            chapter = {
                "chapter_index": i + 1,
                "title": title,
                "content": chapter_content,
                "word_count": len(chapter_content),
                "start_pos": match.start(),
                "end_pos": end_pos,
                "pattern_used": pattern_obj.pattern,
                "pattern_id": pattern_obj.id
            }

            chapters.append(chapter)

        return chapters

    # 这些方法已经在上面重新实现，删除重复的旧版本

    def _split_by_length(self, content: str, chunk_size: int = 10000) -> List[Dict[str, Any]]:
        """按长度分割（降级方案）"""
        chapters = []
        total_length = len(content)
        chapter_index = 1

        for i in range(0, total_length, chunk_size):
            chunk = content[i:i+chunk_size]
            chapters.append({
                "chapter_index": chapter_index,
                "title": f"第{chapter_index}章",
                "content": chunk,
                "word_count": len(chunk),
                "start_pos": i,
                "end_pos": min(i + chunk_size, total_length),
                "pattern_used": "length_split"
            })
            chapter_index += 1

        return chapters

    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        return self.knowledge_base.get_statistics()

    def add_custom_pattern(self, pattern: str, pattern_type: str, source: str = 'manual') -> bool:
        """添加自定义模式"""
        try:
            self.knowledge_base.add_pattern(pattern, pattern_type, source)
            logger.info(f"添加自定义模式: {pattern_type} - {pattern}")
            return True
        except Exception as e:
            logger.error(f"添加自定义模式失败: {e}")
            return False

    def disable_pattern_by_id(self, pattern_id: int) -> bool:
        """禁用指定ID的模式"""
        return self.knowledge_base.disable_pattern(pattern_id)

    def enable_pattern_by_id(self, pattern_id: int) -> bool:
        """启用指定ID的模式"""
        return self.knowledge_base.enable_pattern(pattern_id)

    def flush_pattern_updates(self) -> None:
        """强制刷新模式统计更新"""
        self.knowledge_base.flush_updates()

    def get_memory_summary(self) -> Dict[str, Any]:
        """获取内存使用摘要"""
        return self.memory.get_memory_summary()

    def clear_memory(self, key: Optional[str] = None) -> bool:
        """清空内存数据"""
        return self.memory.clear(key)

    def get_cached_results(self) -> List[Dict[str, Any]]:
        """获取缓存的分割结果"""
        return self.memory.get_cached_results()

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.memory.get_performance_stats()

    def __del__(self):
        """析构函数，确保在Agent销毁时刷新所有待更新的数据"""
        try:
            if hasattr(self, 'knowledge_base') and self.knowledge_base:
                self.knowledge_base.flush_updates()
            if hasattr(self, 'memory') and self.memory:
                # 内存数据在Agent销毁时可以选择性保存
                pass
        except Exception as e:
            logger.error(f"Agent析构时清理失败: {e}")