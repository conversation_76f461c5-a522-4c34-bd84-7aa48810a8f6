"""
章节分割Agent - 垂直架构版本
基于新架构的专业章节分割智能体
"""

import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from loguru import logger

from ...base import BaseAgent, AgentConfig
from ...agent_factory import register_agent
from .knowledge.knowledge_base import ChapterKnowledgeBase

# 使用try-except处理导入，支持动态导入



@register_agent("chapter_split", {
    "description": "专业的章节分割智能体",
    "capabilities": ["智能章节分析", "正则表达式生成", "规则学习优化"],
    "version": "2.0.0"
})
class ChapterSplitAgent(BaseAgent):
    """
    章节分割专业Agent - 垂直架构版本
    
    专门负责小说章节分割任务，集成了：
    - 智能正则表达式生成
    - 章节结构分析  
    - 规则学习和优化
    """
    
    def __init__(self, config: AgentConfig, **kwargs):
        """
        初始化章节分割Agent

        Args:
            config: Agent配置
            **kwargs: 额外参数
        """
        super().__init__(config, **kwargs)

        # 获取数据库会话
        self.db_session = kwargs.get('db')
        if not self.db_session:
            raise ValueError("ChapterSplitAgent需要数据库会话参数")

        # 专业配置
        self.fuzzy_detection_threshold = 30000  # 3万字未找到章节则触发分析
        self.max_analysis_attempts = 3
        self.snippet_size = 3000

        # 初始化专用组件
        self._initialize_specialized_components()

        # 加载知识库
        self._load_knowledge_base()

        logger.info(f"章节分割Agent初始化完成 (垂直架构版本)")
    
    def _initialize_specialized_components(self):
        """初始化专用组件"""
        # 专用工具 - 暂时留空，后续可以添加
        self.regex_tool = None
        self.pattern_tool = None

        # 专用提示模板 - 暂时留空，后续可以添加
        self.analysis_prompt = None
        self.evaluation_prompt = None

        # 专用记忆 - 暂时留空，后续可以添加
        self.pattern_memory = None

    def _load_knowledge_base(self):
        """加载知识库"""
        # 初始化知识库
        self.knowledge_base = ChapterKnowledgeBase(self.db_session)

        # 初始化默认规则
        self.knowledge_base.initialize_default_patterns()

        # 配置知识库参数
        self.knowledge_base.set_config('page_size', 100)
        self.knowledge_base.set_config('max_test_rules', 500)
        self.knowledge_base.set_config('update_delay', 1.0)

        logger.info("章节分割知识库加载完成")
        
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理章节分割请求
        
        Args:
            input_data: 输入数据，包含：
                - content: 小说内容
                - novel_id: 小说ID (可选)
                - force_analysis: 是否强制分析 (可选)
                - method: 分割方法 (可选)
                
        Returns:
            处理结果，包含：
                - chapters: 章节列表
                - quality_score: 质量评分
                - method_used: 使用的方法
                - patterns_found: 发现的模式
        """
        return {"error": "章节分割Agent暂不支持独立运行"}    
      
    
    def smart_chapter_split(self, content: str, force_analysis: bool = False) -> List[Dict[str, Any]]:
        """
        智能章节分割

        Args:
            content: 小说内容
            force_analysis: 是否强制进行Agent分析

        Returns:
            章节列表
        """
        logger.info("开始智能章节分割")

        # 1. 先尝试现有模式（如果不强制分析）
        if not force_analysis:
            chapters = self._split_by_existing_patterns(content)
            if chapters and len(chapters) > 1:
                logger.info(f"使用现有模式成功分割，章节数: {len(chapters)}")
                return chapters

        # 2. 使用LLM分析新模式（暂时返回空，后续实现）
        logger.info("现有模式无效，需要LLM分析...")
        new_patterns = self._agent_analyze_content(content)

        # 3. 如果发现新模式，添加到知识库并重新尝试
        if new_patterns and any(new_patterns.values()):
            self._add_new_patterns_to_knowledge_base(new_patterns)
            chapters = self._split_by_existing_patterns(content)
            if chapters and len(chapters) > 1:
                logger.info(f"使用新模式成功分割，章节数: {len(chapters)}")
                return chapters

        # 4. 降级方案：按长度分割
        logger.warning("所有模式都无效，使用长度分割")
        return self._split_by_length(content)
    
    def _split_by_existing_patterns(self, content: str) -> List[Dict[str, Any]]:
        """使用现有模式分割"""
        import re

        # 按类型依次尝试分割
        for pattern_type in ['intro', 'regular', 'anywhere']:
            chapters = self._try_split_with_pattern_type(content, pattern_type)
            if chapters and len(chapters) > 1:
                return chapters

        return []

    def _try_split_with_pattern_type(self, content: str, pattern_type: str) -> List[Dict[str, Any]]:
        """尝试使用指定类型的模式分割"""
        import re

        page = 1
        tested_rules = 0
        max_rules = self.knowledge_base.get_config('max_test_rules')

        while tested_rules < max_rules:
            # 获取当前页的规则
            patterns = self.knowledge_base.get_patterns_page(pattern_type, page)

            if not patterns:
                # 当前类型没有更多规则
                break

            # 尝试每个规则
            for pattern_obj in patterns:
                tested_rules += 1

                try:
                    # 编译正则表达式
                    regex = re.compile(pattern_obj.pattern)
                    matches = list(regex.finditer(content))

                    if matches and len(matches) > 1:
                        # 找到有效模式，创建章节
                        chapters = self._create_chapters_from_regex_matches(content, matches, pattern_obj)
                        if chapters:
                            # 更新命中统计
                            self.knowledge_base.update_hit_count(pattern_obj.id)
                            logger.info(f"使用规则成功分割: {pattern_obj.pattern}, 章节数: {len(chapters)}")
                            return chapters

                except re.error as e:
                    logger.warning(f"正则表达式错误: {pattern_obj.pattern} - {e}")
                    continue
                except Exception as e:
                    logger.error(f"模式匹配失败: {pattern_obj.pattern} - {e}")
                    continue

                # 检查是否达到测试限制
                if tested_rules >= max_rules:
                    break

            # 如果当前页规则数量小于页大小，说明已经是最后一页
            page_size = self.knowledge_base.get_config('page_size')
            if len(patterns) < page_size:
                break

            page += 1

        logger.debug(f"类型 {pattern_type} 测试了 {tested_rules} 条规则，未找到有效模式")
        return []
    
    def _create_chapters_from_regex_matches(self, content: str, matches: List, pattern_obj) -> List[Dict[str, Any]]:
        """根据正则匹配结果创建章节"""
        chapters = []

        for i, match in enumerate(matches):
            # 获取章节标题
            title = match.group().strip()

            # 计算章节内容范围
            start_pos = match.end()  # 标题后开始
            if i + 1 < len(matches):
                end_pos = matches[i + 1].start()  # 下一个标题前结束
            else:
                end_pos = len(content)  # 最后一章到文末

            # 提取章节内容
            chapter_content = content[start_pos:end_pos].strip()

            # 跳过空章节
            if not chapter_content:
                continue

            chapter = {
                "chapter_index": i + 1,
                "title": title,
                "content": chapter_content,
                "word_count": len(chapter_content),
                "start_pos": match.start(),
                "end_pos": end_pos,
                "pattern_used": pattern_obj.pattern,
                "pattern_id": pattern_obj.id
            }

            chapters.append(chapter)

        return chapters

    def _add_new_patterns_to_knowledge_base(self, patterns: Dict[str, List[str]]) -> None:
        """将新发现的模式添加到知识库"""
        added_count = 0

        for pattern_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                if pattern.strip():
                    try:
                        # 添加新模式，初始命中次数为1（因为是针对当前内容发现的）
                        self.knowledge_base.add_pattern(
                            pattern=pattern,
                            pattern_type=pattern_type,
                            source='llm',
                            initial_hit_count=1
                        )
                        added_count += 1
                    except Exception as e:
                        logger.error(f"添加新模式失败 {pattern}: {e}")

        if added_count > 0:
            logger.info(f"添加了 {added_count} 个新模式到知识库")

    def _agent_analyze_content(self, content: str) -> Dict[str, List[str]]:
        """Agent分析内容，提取新模式"""
        # TODO: 实现LLM分析逻辑
        # 这里暂时返回空，后续可以集成LLM调用
        logger.warning("LLM分析功能暂未实现")
        return {"intro": [], "regular": [], "anywhere": []}

    def _parse_analysis_response(self, response: str) -> Dict[str, List[str]]:
        """解析Agent的分析响应"""
        patterns = {"intro": [], "regular": [], "anywhere": []}
        # TODO: 实现响应解析逻辑
        return patterns

    def _create_chapters_from_matches(self, content: str, matches: List[Dict[str, Any]], pattern: str) -> List[Dict[str, Any]]:
        """根据匹配结果创建章节（兼容旧接口）"""
        chapters = []
        # TODO: 如果需要兼容旧接口，可以在这里实现
        return chapters

    def _split_by_patterns(self, content: str) -> List[Dict[str, Any]]:
        """使用模式分割（公开方法）"""
        return self._split_by_existing_patterns(content)

    def _split_by_length(self, content: str, chunk_size: int = 10000) -> List[Dict[str, Any]]:
        """按长度分割（降级方案）"""
        chapters = []
        total_length = len(content)
        chapter_index = 1

        for i in range(0, total_length, chunk_size):
            chunk = content[i:i+chunk_size]
            chapters.append({
                "chapter_index": chapter_index,
                "title": f"第{chapter_index}章",
                "content": chunk,
                "word_count": len(chunk),
                "start_pos": i,
                "end_pos": min(i + chunk_size, total_length),
                "pattern_used": "length_split"
            })
            chapter_index += 1

        return chapters

    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        return self.knowledge_base.get_statistics()

    def add_custom_pattern(self, pattern: str, pattern_type: str, source: str = 'manual') -> bool:
        """添加自定义模式"""
        try:
            self.knowledge_base.add_pattern(pattern, pattern_type, source)
            logger.info(f"添加自定义模式: {pattern_type} - {pattern}")
            return True
        except Exception as e:
            logger.error(f"添加自定义模式失败: {e}")
            return False

    def disable_pattern_by_id(self, pattern_id: int) -> bool:
        """禁用指定ID的模式"""
        return self.knowledge_base.disable_pattern(pattern_id)

    def enable_pattern_by_id(self, pattern_id: int) -> bool:
        """启用指定ID的模式"""
        return self.knowledge_base.enable_pattern(pattern_id)

    def flush_pattern_updates(self) -> None:
        """强制刷新模式统计更新"""
        self.knowledge_base.flush_updates()

    def __del__(self):
        """析构函数，确保在Agent销毁时刷新所有待更新的数据"""
        try:
            if hasattr(self, 'knowledge_base') and self.knowledge_base:
                self.knowledge_base.flush_updates()
        except Exception as e:
            logger.error(f"Agent析构时刷新更新失败: {e}")