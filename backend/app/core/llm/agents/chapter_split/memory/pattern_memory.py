"""
章节模式记忆
专门存储和管理章节分割模式
"""

import json
from typing import Dict, Any, List, Optional, Set, Union
from pathlib import Path
from datetime import datetime
from loguru import logger
from dataclasses import dataclass

try:
    from ....base import BaseMemory
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BaseMemory


class PatternMemory(BaseMemory):
    """
    章节模式记忆
    
    专门用于存储和管理章节分割的模式和规则
    """
    
    def __init__(self, memory_path: Path):
        super().__init__(
            name="pattern_memory",
            description="章节分割模式记忆"
        )
        self.memory_path = memory_path
        self.patterns_file = memory_path / "patterns.json"
        self.history_file = memory_path / "pattern_history.json"
        
        # 内存中的模式数据
        self.patterns: Dict[str, Set[str]] = {
            "intro": set(),
            "regular": set(),
            "anywhere": set()
        }
        self.pattern_stats: Dict[str, Dict[str, Any]] = {}
        self.pattern_history: List[Dict[str, Any]] = []
        
        # 加载现有数据
        self._load_patterns()
        self._load_history()
    
    def _load_patterns(self):
        """加载模式数据"""
        if self.patterns_file.exists():
            try:
                with open(self.patterns_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 转换为集合
                for pattern_type in ["intro", "regular", "anywhere"]:
                    if pattern_type in data:
                        self.patterns[pattern_type] = set(data[pattern_type])
                
                # 加载统计信息
                if "stats" in data:
                    self.pattern_stats = data["stats"]
                
                logger.debug(f"加载章节模式: {sum(len(p) for p in self.patterns.values())}个")
                
            except Exception as e:
                logger.error(f"加载模式文件失败: {e}")
    
    def _load_history(self):
        """加载历史记录"""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.pattern_history = json.load(f)
                logger.debug(f"加载模式历史: {len(self.pattern_history)}条记录")
            except Exception as e:
                logger.error(f"加载历史文件失败: {e}")
    
    def _save_patterns(self):
        """保存模式数据"""
        try:
            self.memory_path.mkdir(parents=True, exist_ok=True)
            
            # 转换集合为列表
            data = {
                "intro": list(self.patterns["intro"]),
                "regular": list(self.patterns["regular"]),
                "anywhere": list(self.patterns["anywhere"]),
                "stats": self.pattern_stats,
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.patterns_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.debug("保存章节模式成功")
            
        except Exception as e:
            logger.error(f"保存模式文件失败: {e}")
    
    def _save_history(self):
        """保存历史记录"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.pattern_history, f, ensure_ascii=False, indent=2)
            logger.debug("保存模式历史成功")
        except Exception as e:
            logger.error(f"保存历史文件失败: {e}")
    
    def store(self, key: str, value: Any) -> bool:
        """存储记忆"""
        try:
            if key == "patterns":
                return self.add_patterns(value)
            elif key == "pattern_stats":
                self.pattern_stats.update(value)
                self._save_patterns()
                return True
            else:
                # 通用存储
                return self._store_generic(key, value)
        except Exception as e:
            logger.error(f"存储记忆失败: {e}")
            return False
    
    def retrieve(self, key: str) -> Optional[Any]:
        """检索记忆"""
        if key == "patterns":
            return {k: list(v) for k, v in self.patterns.items()}
        elif key == "pattern_stats":
            return self.pattern_stats
        elif key == "pattern_history":
            return self.pattern_history
        else:
            return self._retrieve_generic(key)
    
    def clear(self) -> bool:
        """清空记忆"""
        try:
            self.patterns = {"intro": set(), "regular": set(), "anywhere": set()}
            self.pattern_stats = {}
            self.pattern_history = []
            
            # 删除文件
            if self.patterns_file.exists():
                self.patterns_file.unlink()
            if self.history_file.exists():
                self.history_file.unlink()
            
            logger.info("清空章节模式记忆")
            return True
        except Exception as e:
            logger.error(f"清空记忆失败: {e}")
            return False
    
    def add_patterns(self, patterns: Dict[str, List[str]]) -> bool:
        """
        添加新模式
        
        Args:
            patterns: 模式字典，格式为 {"intro": [...], "regular": [...], "anywhere": [...]}
            
        Returns:
            是否添加成功
        """
        try:
            new_patterns_count = 0
            
            for pattern_type, pattern_list in patterns.items():
                if pattern_type in self.patterns:
                    old_count = len(self.patterns[pattern_type])
                    self.patterns[pattern_type].update(pattern_list)
                    new_count = len(self.patterns[pattern_type])
                    new_patterns_count += (new_count - old_count)
            
            # 记录历史
            if new_patterns_count > 0:
                self.pattern_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "action": "add_patterns",
                    "patterns": patterns,
                    "new_count": new_patterns_count
                })
                
                # 更新统计
                for pattern_type, pattern_list in patterns.items():
                    for pattern in pattern_list:
                        if pattern not in self.pattern_stats:
                            self.pattern_stats[pattern] = {
                                "type": pattern_type,
                                "added_time": datetime.now().isoformat(),
                                "usage_count": 0,
                                "success_count": 0
                            }
                
                self._save_patterns()
                self._save_history()
                
                logger.info(f"添加了{new_patterns_count}个新模式")
            
            return True
            
        except Exception as e:
            logger.error(f"添加模式失败: {e}")
            return False
    
    def get_patterns_by_type(self, pattern_type: str) -> List[str]:
        """
        获取指定类型的模式
        
        Args:
            pattern_type: 模式类型 (intro/regular/anywhere)
            
        Returns:
            模式列表
        """
        return list(self.patterns.get(pattern_type, set()))
    
    def get_all_patterns(self) -> Dict[str, List[str]]:
        """获取所有模式"""
        return {k: list(v) for k, v in self.patterns.items()}
    
    def update_pattern_stats(self, pattern: str, success: bool = True):
        """
        更新模式统计信息
        
        Args:
            pattern: 模式字符串
            success: 是否成功使用
        """
        if pattern in self.pattern_stats:
            self.pattern_stats[pattern]["usage_count"] += 1
            if success:
                self.pattern_stats[pattern]["success_count"] += 1
            self._save_patterns()
    
    def get_pattern_effectiveness(self) -> Dict[str, float]:
        """
        获取模式有效性统计
        
        Returns:
            模式有效性字典 {pattern: effectiveness_ratio}
        """
        effectiveness = {}
        for pattern, stats in self.pattern_stats.items():
            usage_count = stats.get("usage_count", 0)
            success_count = stats.get("success_count", 0)
            if usage_count > 0:
                effectiveness[pattern] = success_count / usage_count
            else:
                effectiveness[pattern] = 0.0
        return effectiveness
    
    def get_top_patterns(self, pattern_type: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最有效的模式
        
        Args:
            pattern_type: 模式类型过滤
            limit: 返回数量限制
            
        Returns:
            排序后的模式列表
        """
        effectiveness = self.get_pattern_effectiveness()
        
        # 过滤和排序
        patterns_with_stats = []
        for pattern, ratio in effectiveness.items():
            stats = self.pattern_stats[pattern]
            if pattern_type is None or stats.get("type") == pattern_type:
                patterns_with_stats.append({
                    "pattern": pattern,
                    "type": stats.get("type"),
                    "effectiveness": ratio,
                    "usage_count": stats.get("usage_count", 0),
                    "success_count": stats.get("success_count", 0)
                })
        
        # 按有效性排序
        patterns_with_stats.sort(key=lambda x: x["effectiveness"], reverse=True)
        return patterns_with_stats[:limit]
    
    def _store_generic(self, key: str, value: Any) -> bool:
        """通用存储"""
        # 这里可以实现通用的键值存储
        return True
    
    def _retrieve_generic(self, key: str) -> Optional[Any]:
        """通用检索"""
        # 这里可以实现通用的键值检索
        return None
