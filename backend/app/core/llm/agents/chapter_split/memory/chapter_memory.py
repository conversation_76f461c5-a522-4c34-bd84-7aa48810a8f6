"""
章节分割内存
管理运行时数据和缓存
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

try:
    from ....base import BaseMemory
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from base import BaseMemory


class ChapterMemory(BaseMemory):
    """
    章节分割内存

    负责管理运行时数据：
    - 缓存活跃的分割规则
    - 存储新发现的规则（待验证）
    - 记录当前分割任务的上下文
    - 临时存储分割结果和统计
    """

    def __init__(self):
        super().__init__(
            name="chapter_memory",
            description="章节分割运行时内存"
        )

        # 运行时数据存储
        self.data = {
            'active_patterns': [],          # 当前活跃的规则
            'new_patterns': [],             # 新发现的规则
            'current_context': {},          # 当前任务上下文
            'split_results': [],            # 分割结果缓存
            'performance_stats': {},        # 性能统计
            'pattern_cache': {},            # 规则缓存
            'session_stats': {},            # 会话统计
        }

        # 缓存配置
        self.cache_config = {
            'max_active_patterns': 200,     # 最大活跃规则数
            'max_new_patterns': 50,         # 最大新规则数
            'max_results_cache': 10,        # 最大结果缓存数
            'cache_ttl': 3600,              # 缓存TTL（秒）
        }

        logger.info("章节分割内存初始化完成")

    def store(self, key: str, value: Any) -> bool:
        """
        存储数据到内存

        Args:
            key: 数据键
            value: 数据值

        Returns:
            是否成功
        """
        try:
            if key in self.data:
                self.data[key] = value
                logger.debug(f"内存存储: {key}")
                return True
            else:
                logger.warning(f"未知的内存键: {key}")
                return False
        except Exception as e:
            logger.error(f"内存存储失败: {e}")
            return False

    def retrieve(self, key: str) -> Optional[Any]:
        """
        从内存检索数据

        Args:
            key: 数据键

        Returns:
            数据值或None
        """
        try:
            return self.data.get(key)
        except Exception as e:
            logger.error(f"内存检索失败: {e}")
            return None

    def clear(self, key: Optional[str] = None) -> bool:
        """
        清空内存数据

        Args:
            key: 要清空的键，None表示清空所有

        Returns:
            是否成功
        """
        try:
            if key is None:
                # 清空所有数据
                for k in self.data:
                    if isinstance(self.data[k], list):
                        self.data[k] = []
                    elif isinstance(self.data[k], dict):
                        self.data[k] = {}
                    else:
                        self.data[k] = None
                logger.info("清空所有内存数据")
            elif key in self.data:
                # 清空指定键
                if isinstance(self.data[key], list):
                    self.data[key] = []
                elif isinstance(self.data[key], dict):
                    self.data[key] = {}
                else:
                    self.data[key] = None
                logger.debug(f"清空内存数据: {key}")
            else:
                logger.warning(f"未知的内存键: {key}")
                return False

            return True
        except Exception as e:
            logger.error(f"清空内存失败: {e}")
            return False

    def add_active_pattern(self, pattern: Dict[str, Any]) -> bool:
        """
        添加活跃规则

        Args:
            pattern: 规则对象

        Returns:
            是否成功
        """
        try:
            active_patterns = self.data['active_patterns']

            # 检查是否已存在
            pattern_id = pattern.get('id')
            if pattern_id and any(p.get('id') == pattern_id for p in active_patterns):
                logger.debug(f"规则已存在于活跃列表: {pattern_id}")
                return True

            # 检查缓存大小限制
            if len(active_patterns) >= self.cache_config['max_active_patterns']:
                # 移除最旧的规则
                active_patterns.pop(0)
                logger.debug("移除最旧的活跃规则")

            active_patterns.append(pattern)
            logger.debug(f"添加活跃规则: {pattern.get('pattern', '')}")
            return True

        except Exception as e:
            logger.error(f"添加活跃规则失败: {e}")
            return False

    def add_new_pattern(self, pattern: Dict[str, Any]) -> bool:
        """
        添加新发现的规则

        Args:
            pattern: 新规则

        Returns:
            是否成功
        """
        try:
            new_patterns = self.data['new_patterns']

            # 检查缓存大小限制
            if len(new_patterns) >= self.cache_config['max_new_patterns']:
                # 移除最旧的规则
                new_patterns.pop(0)
                logger.debug("移除最旧的新规则")

            # 添加时间戳
            pattern['discovered_at'] = datetime.utcnow().isoformat()
            new_patterns.append(pattern)

            logger.debug(f"添加新规则: {pattern.get('pattern', '')}")
            return True

        except Exception as e:
            logger.error(f"添加新规则失败: {e}")
            return False

    def set_context(self, context: Dict[str, Any]) -> bool:
        """
        设置当前任务上下文

        Args:
            context: 上下文信息

        Returns:
            是否成功
        """
        try:
            self.data['current_context'] = context
            logger.debug("设置任务上下文")
            return True
        except Exception as e:
            logger.error(f"设置上下文失败: {e}")
            return False

    def get_context(self) -> Dict[str, Any]:
        """
        获取当前任务上下文

        Returns:
            上下文信息
        """
        return self.data.get('current_context', {})

    def cache_split_result(self, result: Dict[str, Any]) -> bool:
        """
        缓存分割结果

        Args:
            result: 分割结果

        Returns:
            是否成功
        """
        try:
            results_cache = self.data['split_results']

            # 检查缓存大小限制
            if len(results_cache) >= self.cache_config['max_results_cache']:
                # 移除最旧的结果
                results_cache.pop(0)
                logger.debug("移除最旧的分割结果")

            # 添加时间戳
            result['cached_at'] = datetime.utcnow().isoformat()
            results_cache.append(result)

            logger.debug("缓存分割结果")
            return True

        except Exception as e:
            logger.error(f"缓存分割结果失败: {e}")
            return False

    def get_cached_results(self) -> List[Dict[str, Any]]:
        """
        获取缓存的分割结果

        Returns:
            结果列表
        """
        return self.data.get('split_results', [])

    def update_performance_stats(self, stats: Dict[str, Any]) -> bool:
        """
        更新性能统计

        Args:
            stats: 统计信息

        Returns:
            是否成功
        """
        try:
            current_stats = self.data['performance_stats']
            current_stats.update(stats)
            current_stats['last_updated'] = datetime.utcnow().isoformat()

            logger.debug("更新性能统计")
            return True

        except Exception as e:
            logger.error(f"更新性能统计失败: {e}")
            return False

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计

        Returns:
            统计信息
        """
        return self.data.get('performance_stats', {})

    def get_memory_summary(self) -> Dict[str, Any]:
        """
        获取内存使用摘要

        Returns:
            内存摘要
        """
        summary = {
            'active_patterns_count': len(self.data['active_patterns']),
            'new_patterns_count': len(self.data['new_patterns']),
            'cached_results_count': len(self.data['split_results']),
            'has_context': bool(self.data['current_context']),
            'cache_config': self.cache_config.copy(),
            'memory_usage': {
                'active_patterns': f"{len(self.data['active_patterns'])}/{self.cache_config['max_active_patterns']}",
                'new_patterns': f"{len(self.data['new_patterns'])}/{self.cache_config['max_new_patterns']}",
                'cached_results': f"{len(self.data['split_results'])}/{self.cache_config['max_results_cache']}"
            }
        }

        return summary