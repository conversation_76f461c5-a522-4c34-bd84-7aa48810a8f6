"""
章节分割模块

负责章节分割的业务逻辑和数据管理：
1. 管理章节分割相关的表结构
2. 调用ChapterSplitAgent进行算法处理
3. 存储分割结果到数据库
"""

import re
import json
from typing import List, Dict, Any, Generator, Optional
from datetime import datetime
from loguru import logger
from sqlalchemy.orm import Session
from sqlalchemy import text

from ...core.database.models import NovelDocument, NovelChapter, FuzzyChapterLog
from ...core.llm import create_agent

class ChapterSplitter:
    """
    章节分割处理器

    职责：
    1. 管理章节分割的业务逻辑
    2. 调用ChapterSplitAgent进行算法处理
    3. 管理章节相关的数据表
    4. 存储分割结果到数据库
    """

    def __init__(self, db: Session, author_name: Optional[str] = None, work_name: Optional[str] = None):
        """
        初始化章节分割处理器

        Args:
            db: 数据库会话
            author_name: 作者名（用于分组）
            work_name: 作品名（用于分组）
        """
        self.db = db
        self.author_name = author_name
        self.work_name = work_name
        self.table_prefix = None

        # 初始化章节分割Agent
        try:
            self.chapter_agent = create_agent(
                "chapter_split",
                db=db,
                llm_provider="qwen",
                verbose=False
            )
            logger.info("章节分割Agent初始化成功")
        except Exception as e:
            logger.warning(f"章节分割Agent初始化失败: {e}")
            self.chapter_agent = None

        # 如果提供了分组信息，初始化表结构
        if author_name and work_name:
            self._initialize_group_tables()

    def _ensure_default_rules(self):
        """
        默认先清空 ChapterRegexPattern 表，然后注入明文硬编码规则（每条规则一条，便于直观维护）。
        """
        default_rules = [
            # intro
            ("intro", r"序章.*?[\n\r]"),
            ("intro", r"前言.*?[\n\r]"),
            ("intro", r"楔子.*?[\n\r]"),
            ("intro", r"引子.*?[\n\r]"),
            ("intro", r"引言.*?[\n\r]"),
            ("intro", r"序幕.*?[\n\r]"),
            ("intro", r"简介.*?[\n\r]"),
            ("intro", r"前序.*?[\n\r]"),
            # anywhere
            ("anywhere", r"尾声.*?[\n\r]"),
            ("anywhere", r"后记.*?[\n\r]"),
            ("anywhere", r"终章.*?[\n\r]"),
            ("anywhere", r"后序.*?[\n\r]"),
            # regular
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+章.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+节.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+卷.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+集.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+部.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+篇.*?[\n\r]"),
            ("regular", r"第[零一二三四五六七八九十百千万亿\d]+回.*?[\n\r]"),
            ("regular", r"[Cc][Hh][Aa][Pp][Tt][Ee][Rr]\\s*[\\d]+.*?[\\n\\r]"),
            ("regular", r"第\\s*\\d+\\s*章.*?[\\n\\r]"),
            ("regular", r"\\d+[\\.、]\\s*[^\\n]+[\\n\\r]"),
            ("regular", r"正文卷\\s*第[零一二三四五六七八九十百千万亿\\d]+章.*?[\\n\\r]"),
        ]

        # 先清空表
        # self.db.query(ChapterRegexPattern).delete()
        # self.db.commit()
        # 再插入默认规则
        for ptype, pattern in default_rules:
            self.rule_manager.add_rule(pattern, ptype, source="init")

    def _compile_regex(self):
        """
        从数据库加载所有规则，按类型组合并编译正则。
        """
        intro_patterns = [r.pattern for r in self.rule_manager.load_rules("intro")]
        regular_patterns = [r.pattern for r in self.rule_manager.load_rules("regular")]
        anywhere_patterns = [r.pattern for r in self.rule_manager.load_rules("anywhere")]
        p_intro = f"(?P<intro>{'|'.join(intro_patterns)})" if intro_patterns else ''
        p_regular = f"(?P<regular>{'|'.join(regular_patterns)})" if regular_patterns else ''
        p_anywhere = f"(?P<anywhere>{'|'.join(anywhere_patterns)})" if anywhere_patterns else ''
        combined_pattern = '|'.join([p for p in [p_intro, p_regular, p_anywhere] if p])
        if combined_pattern:
            self.compiled_regex = re.compile(combined_pattern)
        else:
            self.compiled_regex = None
        logger.info("章节分割正则表达式已从数据库规则编译/重新编译。")

    def process(self, novel_document: NovelDocument, content_chunks_factory) -> bool:
        """
        处理章节分割。
        1. 先用数据库规则切割。
        2. 若无命中，调用LLM补充规则，再用数据库规则切割。
        3. 若仍无命中，按字数切割。
        content_chunks_factory: 一个可多次调用、每次返回新的内容块生成器的工厂函数。
        """
        logger.info(f"[章节分割] 开始处理小说ID: {novel_document.id}")
        try:
            logger.info(f"[章节分割] 清空小说ID {novel_document.id} 已有章节记录")
            self.db.query(NovelChapter).filter(NovelChapter.novel_document_id == novel_document.id).delete()

            # 1. 只扫描前5万字
            scan_buffer = ""
            scanned = 0
            for chunk in content_chunks_factory():
                scan_buffer += chunk
                scanned += len(chunk)
                if scanned >= 50000:
                    break
            can_match = self._match_chapter(scan_buffer, count_hit=False) is not None
            logger.info(f"[章节分割] 规则扫描结果: {'可匹配章节' if can_match else '不可匹配章节'}")

            if can_match:
                # 规则切割，分块读取（不缓存全部内容）
                logger.info("[章节分割] 规则可匹配，分块切割写入数据库……")
                all_content_gen = content_chunks_factory()
                all_content = ''.join(all_content_gen)
                chapters = list(self._split_chapters_by_rules([all_content]))
                chapter_count = len(chapters)
            else:
                # 规则不可匹配，Agent补充规则
                logger.info("[章节分割] 规则不可匹配，拼接内容调用Agent补充规则……")
                all_content_gen = content_chunks_factory()
                all_content = ''.join(all_content_gen)
                self._agent_analyze_and_update_rules(novel_document.id, all_content)
                # 再次分块切割
                logger.info("[章节分割] Agent补充规则后再次分块切割……")
                chapters = list(self._split_chapters_by_rules([all_content]))
                chapter_count = len(chapters)
                if chapter_count == 0:
                    logger.info("[章节分割] 规则切割和LLM补充规则后仍未命中，分块字数切割……")
                    chapters = list(self._split_by_length(all_content, 10000))
                    chapter_count = len(chapters)

            # 写入数据库
            logger.info(f"[章节分割] 开始写入数据库，共{chapter_count}章……")
            for chapter in chapters:
                logger.info(f"[章节分割] 写入章节: 索引={chapter['chapter_index']}, 标题={chapter['title']}, 字数={chapter['word_count']}")
                novel_chapter = NovelChapter(
                    novel_document_id=novel_document.id,
                    chapter_index=chapter["chapter_index"],
                    title=chapter["title"],
                    content=chapter["content"],
                    word_count=chapter["word_count"]
                )
                self.db.add(novel_chapter)
            self.db.commit()
            logger.info(f"[章节分割] 章节分割完成 - 小说ID: {novel_document.id}, 共{chapter_count}章")
            return True
        except Exception as e:
            logger.exception(f"[章节分割] 章节分割异常 - 小说ID: {novel_document.id}, 错误: {str(e)}")
            self.db.rollback()
            raise
    
    def _split_chapters_by_rules(self, content_chunks: Generator[str, None, None]):
        """
        依次调用 intro/regular/epilogue 切割，拼接最终章节列表。
        若不能匹配到章节，直接返回空列表。
        """
        logger.info("[规则切割] 开始依次切割前言、章节、后记……")
        # 先将内容全部拼成字符串
        if hasattr(content_chunks, '__iter__'):
            content = ''.join(list(content_chunks))
        else:
            content = ''
        result = []
        # 先判断能否匹配到章节
        chapter_match = self._match_chapter(content, count_hit=False)
        if not chapter_match:
            logger.info("[规则切割] 未匹配到任何章节标题，规则切割失败，需LLM补充规则")
            return []
        chapter_start, _, _ = chapter_match
        intro_content = content[:chapter_start].strip()
        rest = content[chapter_start:]
        # 前言
        if intro_content:
            logger.info(f"[规则切割] 前言切割成功，字数: {len(intro_content)}")
            result.append({
                "chapter_index": 0,
                "title": "前言",
                "content": intro_content,
                "word_count": len(intro_content)
            })
        # 章节
        chapters, rest2 = self._split_chapters(rest)
        for ch in chapters:
            result.append(ch)
        # 后记
        if chapters:
            epilogue = self._split_epilogue(rest2, start_index=len(result))
            if epilogue:
                result.append(epilogue)
        logger.info(f"[规则切割] 切割流程结束，最终章节数: {len(result)}")
        return result

    def _split_by_length(self, content: str, chunk_size: int = 10000) -> Generator[Dict[str, Any], None, None]:
        """
        按字数分割小说内容，每 chunk_size 字为一章。
        """
        total_length = len(content)
        chapter_index = 1
        for i in range(0, total_length, chunk_size):
            chunk = content[i:i+chunk_size]
            yield {
                "chapter_index": chapter_index,
                "title": f"第{chapter_index}章",
                "content": chunk,
                "word_count": len(chunk)
            }
            chapter_index += 1 



    def _analyze_and_get_regex(self, novel_document_id: int, snippet_to_analyze: str) -> Optional[dict]:
        """
        调用章节分割Agent分析文本片段，并根据返回的结构化JSON。
        """
        logger.info(f"小说ID {novel_document_id}：调用章节分割Agent确定章节分割规则...")

        try:
            # 检查Agent是否可用
            if not self.chapter_agent:
                logger.error("章节分割Agent未初始化，无法进行分析")
                return None

            logger.info("使用章节分割Agent进行分析...")

            # 构建上下文信息
            context = {
                "intro_patterns": [],
                "regular_patterns": [],
                "anywhere_patterns": []
            }

            # 调用Agent分析
            patterns = self.chapter_agent.analyze_chapter_patterns(
                text_snippet=snippet_to_analyze,
                context=context
            )

            # 将Agent返回的格式转换为原有格式
            if patterns:
                data = {
                    "intro_patterns": patterns.get("intro", []),
                    "regular_patterns": patterns.get("regular", []),
                    "anywhere_patterns": patterns.get("anywhere", [])
                }

                # 对regular_patterns自动加[\n\r]结尾（保持原有逻辑）
                regular_patterns = data.get("regular_patterns", [])
                data["regular_patterns"] = [
                    p if p.endswith('[\\n\\r]') else p + '[\\n\\r]'
                    for p in regular_patterns
                ]

                logger.info(f"Agent分析结果: {data}")

                # 检查是否发现新规则
                found = False
                for ptype in ("intro", "regular", "anywhere"):
                    key = f"{ptype}_patterns"
                    patterns_list = data.get(key, [])
                    if isinstance(patterns_list, list) and patterns_list:
                        found = True
                        for pat in patterns_list:
                            self.rule_manager.add_rule(pat, ptype, source="agent")

                if found:
                    # 记录日志
                    log_entry = FuzzyChapterLog(
                        novel_document_id=novel_document_id,
                        analyzed_content_snippet=snippet_to_analyze,
                        returned_regex=json.dumps(data, ensure_ascii=False),
                    )
                    self.db.add(log_entry)
                    self.db.commit()
                    return data
                else:
                    logger.info("Agent未发现新规则。")
                    return None
            else:
                logger.info("Agent分析未返回有效结果。")
                return None

        except Exception as e:
            logger.error(f"调用章节分析时发生错误: {e}")
            return None




    def _agent_analyze_and_update_rules(self, novel_document_id: int, content: str):
        """
        用Agent分析文本片段，补充规则到数据库。
        """
        max_attempts = 3
        snippet_size = 3000
        all_new_patterns = {"intro": set(), "regular": set(), "anywhere": set()}

        for attempt in range(max_attempts):
            start_index = attempt * snippet_size
            end_index = start_index + snippet_size
            current_snippet = content[start_index:end_index]
            if not current_snippet:
                break

            logger.info(f"Agent规则分析，第 {attempt + 1}/{max_attempts} 轮……")
            analysis_data = self._analyze_and_get_regex(novel_document_id, current_snippet)
            if analysis_data:
                found_regular = False
                for ptype in ("intro", "regular", "anywhere"):
                    key = f"{ptype}_patterns"
                    patterns = analysis_data.get(key, [])
                    if isinstance(patterns, list) and patterns:
                        for pat in patterns:
                            if pat not in all_new_patterns[ptype]:
                                self.rule_manager.add_rule(pat, ptype, source="agent")
                                all_new_patterns[ptype].add(pat)
                                logger.info(f"[规则写入] 类型={ptype}, pattern={pat} —— 已写入数据库")
                            else:
                                logger.info(f"[规则去重] 类型={ptype}, pattern={pat} —— 已存在，跳过写入")
                        if ptype == "regular" and patterns:
                            found_regular = True
                if found_regular:
                    logger.info("Agent已发现有效章节规则，提前终止分析。")
                    break

    
    
    
    
    
    
    def _match_intro(self, content: str):
        """判断并返回前言匹配的span（起止位置）和标题。"""
        logger.info("[前言匹配] 开始匹配前言规则……")
        intro_rules = self.rule_manager.load_rules("intro")
        for rule in intro_rules:
            pat = rule.pattern
            m = re.search(pat, content)
            if m:
                logger.info(f"[前言匹配] 命中前言规则: {pat}，标题: {m.group().strip()}")
                # 记录命中
                self.rule_manager.record_hit(rule.id)
                return m.start(), m.end(), m.group().strip()
        logger.info("[前言匹配] 未命中任何前言规则")
        return None

    def _match_chapter(self, content: str, count_hit: bool = True):
        """判断并返回第一个章节匹配的span和标题。"""
        logger.info("[章节匹配] 开始匹配章节规则……")
        regular_rules = self.rule_manager.load_rules("regular")
        for rule in regular_rules:
            pat = rule.pattern
            m = re.search(pat, content)
            if m:
                logger.info(f"[章节匹配] 命中章节规则: {pat}，标题: {m.group().strip()}")
                # 仅在 count_hit=True 时计数
                if count_hit:
                    self.rule_manager.record_hit(rule.id)
                return m.start(), m.end(), m.group().strip()
        logger.info("[章节匹配] 未命中任何章节规则")
        return None

    def _match_epilogue(self, content: str):
        """判断并返回后记匹配的span和标题。"""
        logger.info("[后记匹配] 开始匹配后记规则……")
        anywhere_rules = self.rule_manager.load_rules("anywhere")
        for rule in anywhere_rules:
            pat = rule.pattern
            m = re.search(pat, content)
            if m:
                logger.info(f"[后记匹配] 命中后记规则: {pat}，标题: {m.group().strip()}")
                # 记录命中
                self.rule_manager.record_hit(rule.id)
                return m.start(), m.end(), m.group().strip()
        logger.info("[后记匹配] 未命中任何后记规则")
        return None
    ##暂不调用
    # def _split_intro(self, content: str):
    #     """切割前言，返回前言章节和剩余内容。"""
    #     logger.info("[前言切割] 开始切割前言……")
    #     match = self._match_intro(content)
    #     if match:
    #         start, end, title = match
    #         intro_content = content[:end].strip()
    #         rest = content[end:]
    #         logger.info(f"[前言切割] 前言切割成功，标题: {title}，字数: {len(intro_content)}")
    #         return {
    #             "chapter_index": 0,
    #             "title": title,
    #             "content": intro_content,
    #             "word_count": len(intro_content)
    #         }, rest
    #     logger.info("[前言切割] 未切割出前言")
    #     return None, content

    def _split_chapters(self, content: str):
        """切割所有章节，返回章节列表和剩余内容。"""
        logger.info("[章节切割] 开始切割章节……")
        chapters = []
        regular_patterns = [r.pattern for r in self.rule_manager.load_rules("regular")]
        if not regular_patterns:
            logger.info("[章节切割] 无章节规则，直接返回")
            return chapters, content
        # 合并正则
        pattern = "|".join(f"({p})" for p in regular_patterns)
        regex = re.compile(pattern)
        matches = list(regex.finditer(content))
        if not matches:
            logger.info("[章节切割] 未匹配到任何章节标题")
            return chapters, content
        for idx, match in enumerate(matches):
            start = match.start()
            end = match.end()
            title = match.group().strip()
            next_start = matches[idx+1].start() if idx+1 < len(matches) else None
            chapter_content = content[end:next_start].strip() if next_start else content[end:].strip()
            logger.info(f"[章节切割] 切割章节: 索引={idx+1}, 标题={title}, 字数={len(chapter_content)}")
            chapters.append({
                "chapter_index": idx+1,
                "title": title,
                "content": chapter_content,
                "word_count": len(chapter_content)
            })
        # 剩余内容为最后一个章节之后
        last_end = matches[-1].end()
        rest = content[last_end:]
        logger.info(f"[章节切割] 章节切割完成，共{len(chapters)}章")
        return chapters, rest

    def _split_epilogue(self, content: str, start_index=1):
        """切割后记，返回后记章节。"""
        logger.info("[后记切割] 开始切割后记……")
        match = self._match_epilogue(content)
        if match:
            start, end, title = match
            epilogue_content = content[end:].strip()
            logger.info(f"[后记切割] 后记切割成功，标题: {title}，字数: {len(epilogue_content)}")
            return {
                "chapter_index": start_index,
                "title": title,
                "content": epilogue_content,
                "word_count": len(epilogue_content)
            }
        logger.info("[后记切割] 未切割出后记")
        return None

    